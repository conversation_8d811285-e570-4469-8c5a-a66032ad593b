%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &43839245131525166
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3892978864895620315}
  - component: {fileID: 400344922097859414}
  - component: {fileID: 7376526528671321591}
  m_Layer: 0
  m_Name: shells
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3892978864895620315
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43839245131525166}
  serializedVersion: 2
  m_LocalRotation: {x: 0.91597563, y: -0.12059048, z: -0.0499502, w: -0.37940952}
  m_LocalPosition: {x: -0.00021914659, y: 0.085669585, z: 0.068817504}
  m_LocalScale: {x: 0.80000013, y: 0.80000013, z: 0.80000013}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5777294461523236845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &400344922097859414
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43839245131525166}
  m_Mesh: {fileID: 3297411833427862167, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
--- !u!23 &7376526528671321591
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 43839245131525166}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6ce9dc103f4107b45ad1a866ce0e197f, type: 2}
  - {fileID: 2100000, guid: 14c2d7e277d4f9d4590795b86bd982be, type: 2}
  - {fileID: 2100000, guid: 83c24d5547e3e9f4382e262a4b8781bf, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &771528457394026854
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7762576261927665378}
  m_Layer: 0
  m_Name: shotgunBarrels
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7762576261927665378
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 771528457394026854}
  serializedVersion: 2
  m_LocalRotation: {x: 0.6578334, y: 0.00000035913084, z: 0.00000031367424, w: 0.7531635}
  m_LocalPosition: {x: 2.4632065e-20, y: -0.00011094159, z: 0.0011043646}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3212545434523557523}
  m_Father: {fileID: 8906046308147068432}
  m_LocalEulerAnglesHint: {x: 82.27, y: 0, z: 0}
--- !u!1 &1873934957689890527
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1973454638689617879}
  - component: {fileID: 2957073913613374702}
  m_Layer: 0
  m_Name: shotgunMesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1973454638689617879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873934957689890527}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5777294461523236845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &2957073913613374702
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1873934957689890527}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5f07c625847716642a1d2cda26395467, type: 2}
  - {fileID: 2100000, guid: 6247e7591a187944cac07b47d75e3be3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -2578597674188618612, guid: 0faba8f2d76279e40b4e279bf82aa54c, type: 3}
  m_Bones:
  - {fileID: 8906046308147068432}
  - {fileID: 7762576261927665378}
  - {fileID: 3833238459782907618}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 8906046308147068432}
  m_AABB:
    m_Center: {x: 5.820766e-11, y: -0.0004417888, z: 0.00087714294}
    m_Extent: {x: 0.00034913432, y: 0.001031038, z: 0.0021444745}
  m_DirtyAABB: 0
--- !u!1 &6250162509443206449
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5777294461523236845}
  - component: {fileID: 3720799477159118377}
  - component: {fileID: 1443203292384323373}
  - component: {fileID: 6431305490174588235}
  - component: {fileID: 7026978510104313899}
  - component: {fileID: 3331831099221948506}
  m_Layer: 0
  m_Name: GunInHandTPO
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5777294461523236845
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3892978864895620315}
  - {fileID: 2976265667970383280}
  - {fileID: 1973454638689617879}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3720799477159118377
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 35f2c96f66e8ee8409b07f79310f62f1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  sceneObject:
    RawGuidValue: ********************************
  ItemID: 16
  _IsAvailable: 1
  inHandTPO:
    RawGuidValue: ********************************
  shootDistance: 50
  hitLayer:
    serializedVersion: 2
    m_Bits: 262144
  shotSound: {fileID: 8300000, guid: 9330601abbfdee347b4b5d0f204ccc19, type: 3}
  audioVolume: 0.5
  muzzleFlashFx: {fileID: 0}
  maxAmmo: 6
  fireRate: 0.5
  _CurrentAmmo: 5
--- !u!114 &1443203292384323373
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: -1552182283, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  SortKey: 300060755
  ObjectInterest: 1
  Flags: 262145
  NestedObjects: []
  NetworkedBehaviours:
  - {fileID: 3720799477159118377}
  - {fileID: 7026978510104313899}
  ForceRemoteRenderTimeframe: 0
--- !u!54 &6431305490174588235
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  serializedVersion: 4
  m_Mass: 1
  m_Drag: 0
  m_AngularDrag: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!114 &7026978510104313899
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 158639473, guid: e725a070cec140c4caffb81624c8c787, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _stateAuthorityChangeErrorCorrectionDelta: 0
  SyncScale: 0
  SyncParent: 0
  _autoAOIOverride: 1
  DisableSharedModeInterpolation: 0
--- !u!65 &3331831099221948506
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6250162509443206449}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.1, y: 0.1, z: 0.4}
  m_Center: {x: 0, y: 0, z: 0.11}
--- !u!1 &6353508107317591004
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3212545434523557523}
  m_Layer: 0
  m_Name: shotgunBarrels_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3212545434523557523
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6353508107317591004}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.0019475808, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7762576261927665378}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6708736331325697041
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 488645935096024078}
  m_Layer: 0
  m_Name: shotgunTrigger_end
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &488645935096024078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6708736331325697041}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.00019612582, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3833238459782907618}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7941547857322836392
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2976265667970383280}
  m_Layer: 0
  m_Name: shotgun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2976265667970383280
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7941547857322836392}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 100, y: 100, z: 100}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8906046308147068432}
  m_Father: {fileID: 5777294461523236845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8110549541791061056
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3833238459782907618}
  m_Layer: 0
  m_Name: shotgunTrigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3833238459782907618
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8110549541791061056}
  serializedVersion: 2
  m_LocalRotation: {x: 0.97572595, y: -0.000000026106243, z: -0.0000001163156, w: 0.21899515}
  m_LocalPosition: {x: 4.3242964e-20, y: -0.00019474901, z: 0.000014791062}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 488645935096024078}
  m_Father: {fileID: 8906046308147068432}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8549310397878664383
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8906046308147068432}
  m_Layer: 0
  m_Name: shotgunRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8906046308147068432
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8549310397878664383}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071068, y: 0, z: -0, w: 0.7071067}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7762576261927665378}
  - {fileID: 3833238459782907618}
  m_Father: {fileID: 2976265667970383280}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
