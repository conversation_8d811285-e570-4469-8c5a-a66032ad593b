using UnityEngine;
using Fusion;
using SimpleFPS;
using DG.Tweening;

namespace SimpleFPS {
    public class GunItem : PickupItem {

        [Header("GunItem Settings")]
        public float shootDistance = 50f;
        public LayerMask hitLayer;

        [Header("Audio")]
        [SerializeField] private AudioClip shotSound; // Shot sound
        [SerializeField] private float audioVolume = 1f;

        [Header("Visual Effects")]
        [SerializeField] private GameObject muzzleFlashFx; // Muzzle flash particles

        [Header("Ammo settings")]
        public int maxAmmo = 6; // Maximum number of shots

        // Ammo tracking (networked)
        [Networked] public int CurrentAmmo { get; set; }

        private AudioSource audioSource;

        public override void Spawned() {
            base.Spawned();

            // Initialize ammo
            if (Object.HasStateAuthority) {
                CurrentAmmo = maxAmmo;
            }

            // Initialize AudioSource
            audioSource = GetComponent<AudioSource>();
            if (audioSource == null) {
                audioSource = gameObject.AddComponent<AudioSource>();
            }

            // Configure AudioSource
            audioSource.playOnAwake = false;
            audioSource.loop = false;
            audioSource.volume = audioVolume;
            audioSource.spatialBlend = 1f; // 3D sound

            // Initialize muzzle flash particles (disabled by default)
            if (muzzleFlashFx != null) {
                muzzleFlashFx.SetActive(false);
            }
        }

        public override void ItemAbility(PlayerController player) {
            if (!Object.HasInputAuthority) return;

            // Check if gun has ammo
            if (CurrentAmmo <= 0) {
                return;
            }

            // Play shot effects locally
            if (audioSource != null && shotSound != null) {
                audioSource.PlayOneShot(shotSound, audioVolume);
            }

            // Activate muzzle flash particles locally
            if (muzzleFlashFx != null) {
                muzzleFlashFx.SetActive(false); // Reset the effect
                muzzleFlashFx.SetActive(true);  // Activate to play the effect
            }

            RPC_AttemptShoot(player.Object.InputAuthority);
        }

        // state‑authority: ищем Hitbox через LagCompensation
        [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
        void RPC_AttemptShoot(PlayerRef shooterRef, RpcInfo _ = default) {

            if (!Object.HasStateAuthority) {
                return;
            }

            // Check ammo on server
            if (CurrentAmmo <= 0) {
                return;
            }

            // Consume ammo
            CurrentAmmo--;

            var shooterObj = Runner.GetPlayerObject(shooterRef);

            if (!shooterObj) {
                return;
            }
            var shooter = shooterObj.GetComponent<PlayerController>();

            Vector3 origin = shooter.laserBeamSource2.position;
            Vector3 direction = shooter.laserBeamSource2.forward;

            if (Runner.LagCompensation.Raycast(origin,
                                               direction,
                                               shootDistance,
                                               shooterRef,
                                               out LagCompensatedHit hit,
                                               hitLayer == 0 ? ~0 : hitLayer,
                                               HitOptions.IncludePhysX)) {

                var root = hit.Hitbox.Root;
                PlayerController victim = root ? root.GetComponent<PlayerController>() : null;

                var throwerObj = Runner.GetPlayerObject(Object.InputAuthority);
                PlayerController thrower = throwerObj ? throwerObj.GetComponent<PlayerController>() : null;

                if (victim && victim.Object.InputAuthority != shooterRef) {
                    // Actually kill the player using PlayerDeath component
                    PlayerDeath playerDeath = victim.GetComponent<PlayerDeath>();
                    if (playerDeath != null) {
                        playerDeath.DieFromExplosion(shooterRef);
                    }
                }

                if (thrower) {
                    thrower.RPC_ShowHitMarker();
                }

                if (victim) {
                    victim.RPC_ShowGotHit();
                }
            }
        }


        // Public methods for UI or other systems to check ammo status
        public bool HasAmmo() {
            return CurrentAmmo > 0;
        }

        public int GetCurrentAmmo() {
            return CurrentAmmo;
        }

        public int GetMaxAmmo() {
            return maxAmmo;
        }

        public void ReloadAmmo() {
            if (Object.HasStateAuthority) {
                CurrentAmmo = maxAmmo;
            }
        }

        public override void AttachToPlayer(PlayerController player) {
            transform.SetParent(player.thirdPersonItemHolder, false);

            transform.localPosition = new Vector3(-0.0682160929f, 0.0907780975f, -0.0053340923f);
            transform.localRotation = Quaternion.Euler(271.528534f, 58.2589722f, 39.6336441f);
            transform.localScale = Vector3.one;

            SetLayerRecursively(gameObject,
                player.HasInputAuthority ? LayerMask.NameToLayer("LocalPlayerBody")
                                         : LayerMask.NameToLayer("ClientPlayerBody"));

            if (TryGetComponent(out Collider col)) {
                col.enabled = false;
            }

            if (TryGetComponent(out Rigidbody rb)) {
                rb.isKinematic = true;
            }

            // Set gun holding animation via RPC for network sync
            RPC_SetHoldingAnimation(player.Object.InputAuthority, true);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_SetHoldingAnimation(PlayerRef playerRef, bool isHolding) {
            PlayerController player = Runner.GetPlayerObject(playerRef).GetComponent<PlayerController>();
            if (player != null && player.animator != null) {
                player.animator.SetBool("isHoldingShotgun", isHolding);
            }
        }

        public override void RequestToUse(PlayerRef playerRef) {
            RPC_RequestPickup(playerRef);
        }

        public override void RequestDrop() {
            if (Object.HasInputAuthority) {
                RPC_RequestDrop();
            }
        }

        [Rpc(RpcSources.All, RpcTargets.StateAuthority)]
        private new void RPC_RequestPickup(PlayerRef playerRef) {
            if (!IsCanBeUse()) {
                return;
            }

            // Use standard pickup logic from base class
            base.RPC_RequestPickup(playerRef);
        }

        [Rpc(RpcSources.InputAuthority, RpcTargets.StateAuthority)]
        private void RPC_RequestDrop() {
            // Get player and save playerRef before removing authority
            PlayerRef playerRef = Object.InputAuthority;
            PlayerController player = Runner.GetPlayerObject(playerRef).GetComponent<PlayerController>();
            if (player == null) return;

            // Drop position and rotation
            Vector3 dropPos = player.firstPersonItemHolder.position;
            Quaternion dropRot = Quaternion.Euler(0, 0, 0);

            // Reset gun holding animation BEFORE removing authority
            RPC_SetHoldingAnimation(playerRef, false);

            // Remove input authority and free player hands
            Object.RemoveInputAuthority();
            IsAvailable = true;
            player.CurrentItem = null;

            // Apply visual changes
            RPC_ApplyDropVisuals(dropPos, dropRot);
        }

        [Rpc(RpcSources.StateAuthority, RpcTargets.All)]
        private void RPC_ApplyDropVisuals(Vector3 pos, Quaternion rot) {
            transform.SetParent(null, true);
            transform.SetPositionAndRotation(pos, rot);

            if (TryGetComponent(out Collider col)) {
                col.enabled = true;
            }
            if (TryGetComponent(out Rigidbody rb)) {
                rb.isKinematic = false;
            }

            SetLayerRecursively(gameObject, LayerMask.NameToLayer("Default"));
        }
    }
}
